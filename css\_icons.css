/* ===== ÍCONES SVG ===== */

/* Base SVG Icon Styles */
.icon-svg {
    width: 1.2em;
    height: 1.2em;
    fill: currentColor;
    display: inline-block !important;
    vertical-align: middle;
    flex-shrink: 0;
    transition: all 0.2s ease;
    visibility: visible !important;
    opacity: 1 !important;
}

/* Specific icon colors */
.icon-whatsapp {
    color: #25d366 !important;
    fill: #25d366 !important;
}

.icon-instagram {
    color: #e4405f !important;
    fill: #e4405f !important;
}

.icon-location {
    color: #4285f4 !important;
    fill: #4285f4 !important;
}

.icon-website {
    color: #6c5ce7 !important;
    fill: #6c5ce7 !important;
}

/* Button content layout fixes - Override external CSS */
.button-content {
    display: flex !important;
    align-items: center !important;
    gap: 16px !important;
}

.button-content .icon-svg {
    width: 28px !important;
    height: 28px !important;
    min-width: 28px !important;
    font-size: 28px !important;
}

/* Override external CSS for link buttons */
.link-button .button-content .icon-svg {
    width: 32px !important;
    height: 32px !important;
    min-width: 32px !important;
}

/* Arrow icons */
.arrow {
    width: 16px;
    height: 16px;
    opacity: 0.7;
}

/* Footer social icons */
.social-icons .icon-svg {
    width: 20px;
    height: 20px;
    margin: 0 8px;
    opacity: 0.8;
    transition: opacity 0.2s ease;
}

.social-icons .icon-svg:hover {
    opacity: 1;
}

/* Modal and tab icons */
.config-header .icon-svg,
.tab-btn .icon-svg,
.section-title .icon-svg {
    width: 18px;
    height: 18px;
    margin-right: 8px;
}

/* Form button icons */
.btn-expand-form .icon-svg,
.btn-add-link .icon-svg,
.btn-cancel-form .icon-svg,
.btn-primary .icon-svg,
.btn-secondary .icon-svg {
    width: 16px;
    height: 16px;
    margin-right: 6px;
}

/* Close button icons */
.config-close .icon-svg,
.edit-close .icon-svg {
    width: 20px;
    height: 20px;
}

/* Skip Link para Acessibilidade */
.skip-link {
    position: absolute;
    top: -40px;
    left: 6px;
    background: #000;
    color: #fff;
    padding: 8px;
    text-decoration: none;
    border-radius: 4px;
    z-index: 1000;
    font-size: 14px;
    transition: top 0.3s;
}
.skip-link:focus {
    top: 6px;
}

/* Melhorar contraste para acessibilidade */
.footer p {
    color: #e0e0e0;
}

/* Ensure SVGs work in all browsers */
svg {
    pointer-events: none;
}

/* Fix for Safari SVG rendering */
.icon-svg path {
    vector-effect: non-scaling-stroke;
}

/* Responsive adjustments for mobile */
@media (max-width: 768px) {
    .button-content .icon-svg {
        width: 24px !important;
        height: 24px !important;
        min-width: 24px !important;
    }

    .link-button .button-content .icon-svg {
        width: 28px !important;
        height: 28px !important;
        min-width: 28px !important;
    }
}

/* Desktop adjustments */
@media (min-width: 1024px) {
    .button-content .icon-svg {
        width: 32px !important;
        height: 32px !important;
        min-width: 32px !important;
    }

    .link-button .button-content .icon-svg {
        width: 36px !important;
        height: 36px !important;
        min-width: 36px !important;
    }
}
